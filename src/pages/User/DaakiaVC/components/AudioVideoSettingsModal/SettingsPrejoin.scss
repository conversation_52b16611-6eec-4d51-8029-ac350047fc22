@import "../../styles/variables";

.settings-prejoin-modal {
  // Ensure perfect centering
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    height: 70vh;
    max-height: 700px;
    min-height: 500px;

    .ant-modal-header {
      padding: 5px 15px 5px 15px;
      border-bottom: 1px solid #D7D7D7;
      margin-bottom: 0;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      width: 100%;
      height: 46px; // Fixed height to ensure consistent alignment

      .ant-modal-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        padding: 0;
        color: #636363;
        line-height: 1;
        display: flex;
        align-items: center;
        flex: 1;
        order: 1; // Ensure title comes first
      }
    }

    // Close button styling - aligned with title
    .ant-modal-close {
      position: relative;
      margin: 0;
      padding: 0;
      z-index: 10;
      order: 2; // Ensure close button comes after title
      flex-shrink: 0;

      .ant-modal-close-x {
        font-size: 16px;
        color: #666;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f5f5;
          color: #333;
        }
      }
    }

    .ant-modal-body {
      padding: 0;
      height: calc(100% - 60px);
      overflow: hidden;

      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: row;

        .ant-tabs-nav {
          width: 200px;
          margin: 0;
          flex-shrink: 0;

          .ant-tabs-nav-list {
            width: 100%;

            .ant-tabs-tab {
              padding: 1rem 1.5rem;
              margin: 0;
              border-radius: 0;
              width: 100%;

              &.ant-tabs-tab-active {
                background-color: #d5eaff;
                border-color: transparent;
              }

              .tab-label {
                font-family: $font;
                font-weight: 500;
                font-size: 16px;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                width: 100%;

                svg {
                  width: 22px;
                  height: 22px;
                  flex-shrink: 0;
                }

                span {
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: 2px solid #eff1f4;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 3px;
              }
            }
          }
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.settings-content {
  padding: 2rem;
  height: auto;
  max-height: 100%;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
}

.settings-section {
  margin-bottom: 2rem;

  h3 {
    font-size: 22px;
    font-family: $font;
    font-weight: 600;
    color: #000;
    margin-bottom: 0.5rem;
  }

  .settings-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
  }
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;

  .setting-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(90deg, rgba(59, 96, 228, 1) 0%, rgba(86, 123, 255, 1) 100%);
    border-radius: 50%;
    padding: 4px;
    flex-shrink: 0;
  }

  .setting-details {
    display: flex;
    flex-direction: column;

    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      font-family: $font;
    }

    .setting-sublabel {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
  }

  .setting-label {
    font-size: 16px;
    font-weight: 500;
    color: #000;
    font-family: $font;
  }
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .ant-select {
    .ant-select-selector {
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      &:hover {
        border-color: #40a9ff;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-slider {
    .ant-slider-rail {
      background-color: #f0f0f0;
    }

    .ant-slider-track {
      background-color: #277bf7;
    }

    .ant-slider-handle {
      border-color: #277bf7;

      &:hover {
        border-color: #1e6bd6;
      }

      &:focus {
        border-color: #1e6bd6;
        box-shadow: 0 0 0 5px rgba(39, 123, 247, 0.12);
      }
    }
  }

  .ant-switch-checked {
    background-color: #277bf7 !important;
  }

  .switch-status {
    font-size: 12px;
    color: #666;
    margin-left: 0.5rem;
  }

  .test-button {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background-color: #e6f7ff;
      border-color: #40a9ff;
      color: #1890ff;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .level-indicator {
    width: 200px;

    .ant-progress {
      .ant-progress-bg {
        transition: width 0.1s ease;
      }
    }
  }

  .brightness-value {
    font-family: $font;
    font-weight: 500;
    color: #666;
    min-width: 40px;
    text-align: right;
    margin-left: 0.5rem;
  }
}

// Responsive design
@media screen and (max-width: 1200px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 70vw !important;
    }
  }
}

@media screen and (max-width: 1000px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 80vw !important;
    }
  }
}

@media screen and (max-width: 800px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 90vw !important;
      height: 70vh;
    }

    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 160px;
        }
      }
    }
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 600px) {
  .settings-prejoin-modal {
    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 140px;

          .ant-tabs-tab {
            padding: 0.8rem 1rem;

            .tab-label {
              gap: 0.5rem;

              svg {
                width: 18px;
                height: 18px;
              }

              span {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}